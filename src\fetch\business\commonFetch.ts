import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';

const provinceCityCountryList = {
  code: '0000',
  data: [
    {
      id: 1,
      name: '北京',
      children: [
        {
          id: 11,
          name: '北京市',
          children: [
            { id: 111, name: '东城区', children: [] },
            { id: 112, name: '西城区', children: [] },
            { id: 113, name: '朝阳区', children: [] },
            { id: 114, name: '丰台区', children: [] },
            { id: 115, name: '石景山区', children: [] },
            { id: 116, name: '海淀区', children: [] },
            { id: 117, name: '门头沟区', children: [] },
            { id: 118, name: '房山区', children: [] },
            { id: 119, name: '通州区', children: [] },
            { id: 120, name: '顺义区', children: [] },
            { id: 121, name: '昌平区', children: [] },
            { id: 122, name: '大兴区', children: [] },
            { id: 123, name: '怀柔区', children: [] },
            { id: 124, name: '平谷区', children: [] },
            { id: 125, name: '密云区', children: [] },
            { id: 126, name: '延庆区', children: [] },
          ],
        },
      ],
    },
    {
      id: 2,
      name: '上海',
      children: [
        {
          id: 21,
          name: '上海市',
          children: [
            { id: 211, name: '黄浦区', children: [] },
            { id: 212, name: '徐汇区', children: [] },
            { id: 213, name: '长宁区', children: [] },
            { id: 214, name: '静安区', children: [] },
            { id: 215, name: '普陀区', children: [] },
            { id: 216, name: '虹口区', children: [] },
            { id: 217, name: '杨浦区', children: [] },
            { id: 218, name: '闵行区', children: [] },
            { id: 219, name: '宝山区', children: [] },
            { id: 220, name: '嘉定区', children: [] },
            { id: 221, name: '浦东新区', children: [] },
            { id: 222, name: '金山区', children: [] },
            { id: 223, name: '松江区', children: [] },
            { id: 224, name: '青浦区', children: [] },
            { id: 225, name: '奉贤区', children: [] },
            { id: 226, name: '崇明区', children: [] },
          ],
        },
      ],
    },
    {
      id: 3,
      name: '广东',
      children: [
        {
          id: 31,
          name: '广州市',
          children: [
            { id: 311, name: '荔湾区', children: [] },
            { id: 312, name: '越秀区', children: [] },
            { id: 313, name: '海珠区', children: [] },
            { id: 314, name: '天河区', children: [] },
            { id: 315, name: '白云区', children: [] },
            { id: 316, name: '黄埔区', children: [] },
            { id: 317, name: '番禺区', children: [] },
            { id: 318, name: '花都区', children: [] },
            { id: 319, name: '南沙区', children: [] },
            { id: 320, name: '从化区', children: [] },
            { id: 321, name: '增城区', children: [] },
          ],
        },
        {
          id: 32,
          name: '深圳市',
          children: [
            { id: 321, name: '罗湖区', children: [] },
            { id: 322, name: '福田区', children: [] },
            { id: 323, name: '南山区', children: [] },
            { id: 324, name: '宝安区', children: [] },
            { id: 325, name: '龙岗区', children: [] },
            { id: 326, name: '盐田区', children: [] },
            { id: 327, name: '龙华区', children: [] },
            { id: 328, name: '坪山区', children: [] },
            { id: 329, name: '光明区', children: [] },
            { id: 330, name: '大鹏新区', children: [] },
          ],
        },
      ],
    },
    // 异常情况：空名称
    {
      id: 4,
      name: '',
      children: [
        {
          id: 41,
          name: '测试市',
          children: [{ id: 411, name: '测试区', children: [] }],
        },
      ],
    },
    // 异常情况：null children
    {
      id: 5,
      name: '测试省份',
      children: null,
    },
    // 异常情况：超长名称
    {
      id: 6,
      name: '这是一个非常非常非常非常非常非常非常非常非常非常长的省份名称用于测试前端显示',
      children: [
        {
          id: 61,
          name: '这是一个非常非常非常非常非常非常非常非常非常非常长的城市名称用于测试前端显示',
          children: [
            {
              id: 611,
              name: '这是一个非常非常非常非常非常非常非常非常非常非常长的区县名称用于测试前端显示',
              children: [],
            },
          ],
        },
      ],
    },
  ],
};

const provinceAgencyAreaList = {
  code: '0000',
  data: [
    {
      code: 'BJ',
      name: '北京省区',
      children: [
        {
          code: 'BJ_NORTH',
          name: '北京北区',
          children: [
            { code: 'BJ_NORTH_001', name: '朝阳片区', children: [] },
            { code: 'BJ_NORTH_002', name: '海淀片区', children: [] },
            { code: 'BJ_NORTH_003', name: '昌平片区', children: [] },
          ],
        },
        {
          code: 'BJ_SOUTH',
          name: '北京南区',
          children: [
            { code: 'BJ_SOUTH_001', name: '丰台片区', children: [] },
            { code: 'BJ_SOUTH_002', name: '大兴片区', children: [] },
            { code: 'BJ_SOUTH_003', name: '房山片区', children: [] },
          ],
        },
      ],
    },
    {
      code: 'SH',
      name: '上海省区',
      children: [
        {
          code: 'SH_EAST',
          name: '上海东区',
          children: [
            { code: 'SH_EAST_001', name: '浦东片区', children: [] },
            { code: 'SH_EAST_002', name: '杨浦片区', children: [] },
          ],
        },
        {
          code: 'SH_WEST',
          name: '上海西区',
          children: [
            { code: 'SH_WEST_001', name: '徐汇片区', children: [] },
            { code: 'SH_WEST_002', name: '长宁片区', children: [] },
          ],
        },
      ],
    },
    // 异常情况：空code
    {
      code: '',
      name: '测试省区',
      children: [
        {
          code: 'TEST_001',
          name: '测试区域',
          children: [{ code: 'TEST_001_001', name: '测试片区', children: [] }],
        },
      ],
    },
    // 异常情况：null值
    {
      code: null,
      name: null,
      children: null,
    },
    // 异常情况：特殊字符
    {
      code: 'SPECIAL_@#$%',
      name: '特殊字符省区@#$%^&*()',
      children: [
        {
          code: 'SPECIAL_@#$%_001',
          name: '特殊字符区域@#$%^&*()',
          children: [
            {
              code: 'SPECIAL_@#$%_001_001',
              name: '特殊字符片区@#$%^&*()',
              children: [],
            },
          ],
        },
      ],
    },
  ],
};

const stationList = {
  code: '0000',
  data: [
    { stationName: '北京朝阳站', stationNumber: 'BJ_CY_001' },
    { stationName: '北京海淀站', stationNumber: 'BJ_HD_002' },
    { stationName: '上海浦东站', stationNumber: 'SH_PD_001' },
    { stationName: '上海徐汇站', stationNumber: 'SH_XH_002' },
    { stationName: '深圳南山站', stationNumber: 'SZ_NS_001' },
    { stationName: '深圳宝安站', stationNumber: 'SZ_BA_002' },
    { stationName: '广州天河站', stationNumber: 'GZ_TH_001' },
    { stationName: '广州番禺站', stationNumber: 'GZ_PY_002' },
    // 异常情况：空站点名称
    { stationName: '1', stationNumber: 'EMPTY_001' },
    // 异常情况：空站点编号
    { stationName: '测试站点', stationNumber: '2222' },
    // 异常情况：null值
    { stationName: null, stationNumber: null },
    // 异常情况：超长名称
    {
      stationName:
        '这是一个非常非常非常非常非常非常非常非常非常非常长的站点名称用于测试前端显示效果',
      stationNumber: 'VERY_LONG_STATION_NAME_FOR_TESTING_FRONTEND_DISPLAY_001',
    },
    // 异常情况：特殊字符
    {
      stationName: '特殊字符站点@#$%^&*()',
      stationNumber: 'SPECIAL_CHAR_@#$%_001',
    },
    // 异常情况：数字开头的站点编号
    { stationName: '数字开头站点', stationNumber: '123_NUMERIC_START' },
  ],
};

const vehicleModelList = {
  code: '0000',
  data: [
    { vehicleModelType: 'MODEL_A', vehicleModelName: 'A型无人配送车' },
    { vehicleModelType: 'MODEL_B', vehicleModelName: 'B型无人配送车' },
    { vehicleModelType: 'MODEL_C', vehicleModelName: 'C型无人配送车' },
    { vehicleModelType: 'MODEL_D', vehicleModelName: 'D型无人配送车' },
    { vehicleModelType: 'MODEL_E', vehicleModelName: 'E型无人配送车' },
    { vehicleModelType: 'HEAVY_DUTY', vehicleModelName: '重型配送车' },
    { vehicleModelType: 'LIGHT_DUTY', vehicleModelName: '轻型配送车' },
    { vehicleModelType: 'CAMPUS', vehicleModelName: '校园配送车' },
    { vehicleModelType: 'URBAN', vehicleModelName: '城市配送车' },
    { vehicleModelType: 'SUBURBAN', vehicleModelName: '郊区配送车' },
    // 异常情况：空车型
    { vehicleModelType: '2', vehicleModelName: '空车型测试' },
    // 异常情况：空车型名称
    { vehicleModelType: 'EMPTY_NAME', vehicleModelName: '' },
    // 异常情况：null值
    { vehicleModelType: null, vehicleModelName: null },
    // 异常情况：超长名称
    {
      vehicleModelType: 'VERY_LONG_MODEL_TYPE_FOR_TESTING',
      vehicleModelName:
        '这是一个非常非常非常非常非常非常非常非常非常非常长的车型名称用于测试前端显示效果',
    },
    // 异常情况：特殊字符
    {
      vehicleModelType: 'SPECIAL_@#$%',
      vehicleModelName: '特殊字符车型@#$%^&*()',
    },
    // 异常情况：数字开头
    { vehicleModelType: '123_NUMERIC', vehicleModelName: '123数字开头车型' },
  ],
};
export interface CascaderOption {
  id?: number;
  code?: string;
  name: string;
  children?: CascaderOption[];
}

export interface StationInfo {
  stationName: string;
  stationNumber: string;
}

export interface VehicleModelInfo {
  vehicleModelType: string;
  vehicleModelName: string;
}

export enum downLoadUrlType {
  CARD = 2,
  VEHICLE = 1,
}
class CommonApi {
  getStationDepartment(params: {
    stationProductType?: 'vehicle' | 'robot' | 'integrate';
    cityIdList?: any[];
    stationUseCaseList?: any[];
    stationType?: string;
    enable?: any;
    companyNumber?: string;
  }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_cascade_station_address_list',
      body: params,
    };
    return request(options);
  }

  getCityDepartment() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/get_state_city_address_list',
    };
    return request(options);
  }

  getCommonDropDown({ keyList }: { keyList: Array<string> }) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_down_list',
      body: { keyList },
    };
    return request(options);
  }

  fetchERP() {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/common/common_get_erp_list',
    };
    return request(options);
  }

  getDownloadURL(type: downLoadUrlType) {
    const options: RequestOptions = {
      method: 'POST',
      path: '/k2/management/upload/get_download_url',
      body: {
        type,
      },
    };
    return request(options);
  }

  async upload(requestType, params: { bucketName: string; fileKey: string }) {
    let uploadUrl = '';
    if (requestType == 'VEHICLE_BATCH_ADD') {
      uploadUrl = '/k2/management/upload/device_batch_add';
    } else if (requestType == 'VEHICLE_CARD_NO_BATCH_ADD') {
      uploadUrl = '/k2/management/upload/device_card_no_batch_add';
    }
    const options: RequestOptions = {
      method: 'POST',
      path: uploadUrl,
      body: params,
    };
    return request(options);
  }

  // 获取省市区cascader下拉框
  async getProvinceCityCountryList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/get_province_city_country_list',
      method: Method.POST,
      body: {},
    };
    console.log('调用省市区下拉框接口了！！');
    return Promise.resolve(provinceCityCountryList);
    return request(requestOptions);
  }

  // 获取省区分区下拉框
  async getProvinceAgencyAreaList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/get_province_agency_area_list',
      method: Method.POST,
      body: {},
    };
    console.log('调用省区分区下拉框接口了！！');
    return Promise.resolve(provinceAgencyAreaList);
    return request(requestOptions);
  }

  // 获取站点列表下拉框
  async getStationList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/station_base/get_station_list',
      method: Method.POST,
      body: {},
    };
    console.log('调用站点列表下拉框接口了！！');
    return Promise.resolve(stationList);
    return request(requestOptions);
  }

  // 获取下单车型列表下拉框
  async getVehicleModelList(): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/deployment/requirement/getVehicleModleList',
      method: Method.POST,
      body: {},
    };
    console.log('调用车辆型号下拉框接口了！！');
    return Promise.resolve(vehicleModelList);
    return request(requestOptions);
  }

  // 根据用户erp获取详细信息
  async getStationInfoByErp(erp: string): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/getStationInfoByErp',
      method: Method.POST,
      body: { erp },
    };
    console.log('调用根据ERP获取用户信息接口了！！', erp);
    return Promise.resolve(generateUserInfoByErp(erp));
    return request(requestOptions);
  }

  // 通过站点number查询站点信息
  async getQlStationInfoByNumber(stationNumber: string): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/common/getQlStationInfoByNumber',
      method: Method.POST,
      body: { stationNumber },
    };
    console.log('调用根据站点编号获取站点信息接口了！！', stationNumber);
    return Promise.resolve(generateStationInfoByNumber(stationNumber));
    return request(requestOptions);
  }

  // 根据车号获取车架&车型
  async getVehicleDetailByName(vehicleName: string): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/k2/management/device/get_detail_by_name',
      method: Method.POST,
      body: { vehicleName },
    };
    console.log('调用根据车号获取车架&车型接口了！！', vehicleName);
    return Promise.resolve(generateVehicleDetailByName(vehicleName));
    return request(requestOptions);
  }
}

// 根据ERP获取用户信息Mock数据生成函数
const generateUserInfoByErp = (erp: string) => {
  const random = Math.random();
  const userNames = [
    '张三',
    '李四',
    '王五',
    '赵六',
    '钱七',
    '孙八',
    '周九',
    '吴十',
  ];
  const stationNames = [
    '北京朝阳站',
    '上海浦东站',
    '广州天河站',
    '深圳南山站',
    '杭州西湖站',
  ];
  const addresses = [
    '北京市朝阳区建国路88号',
    '上海市浦东新区陆家嘴环路1000号',
    '广州市天河区珠江新城',
    '深圳市南山区科技园南区',
    '杭州市西湖区文三路',
  ];

  const nameIndex = Math.floor(random * userNames.length);
  const stationIndex = Math.floor(random * stationNames.length);

  // 如果ERP不存在，返回空数据
  if (!erp || erp.length < 3) {
    return {
      code: '0001',
      message: '用户不存在',
      data: null,
    };
  }

  return {
    code: '0000',
    message: 'ok',
    data: {
      name: userNames[nameIndex],
      mail: `${erp}@jd.com`,
      erp: erp,
      contactPhone: '15626496640',
      stationName: stationNames[stationIndex],
      stationNumber: `ST${String(stationIndex + 1).padStart(3, '0')}`,
      provinceId: stationIndex + 1,
      provinceName: ['北京', '上海', '广东', '广东', '浙江'][stationIndex],
      cityId: (stationIndex + 1) * 10 + 1,
      cityName: ['北京市', '上海市', '广州市', '深圳市', '杭州市'][
        stationIndex
      ],
      countryId: (stationIndex + 1) * 100 + 11,
      countryName: ['朝阳区', '浦东新区', '天河区', '南山区', '西湖区'][
        stationIndex
      ],
      address: addresses[stationIndex],
      provinceCompanyCode: `PC${String(stationIndex + 1).padStart(3, '0')}`,
      provinceCompanyName: [
        '北京省区',
        '上海省区',
        '广东省区',
        '广东省区',
        '浙江省区',
      ][stationIndex],
      areaCode: `AC${String(stationIndex + 1).padStart(3, '0')}`,
      areaName: ['北京北区', '上海东区', '广州片区', '深圳片区', '杭州片区'][
        stationIndex
      ],
    },
  };
};

// 根据站点编号获取站点信息Mock数据生成函数
const generateStationInfoByNumber = (stationNumber: string) => {
  const random = Math.random();
  const stationNames = [
    '北京朝阳站',
    '上海浦东站',
    '广州天河站',
    '深圳南山站',
    '杭州西湖站',
  ];
  const contacts = ['张三', '李四', '王五', '赵六', '钱七'];
  const phones = [
    '13800138001',
    '13800138002',
    '13800138003',
    '13800138004',
    '13800138005',
  ];
  const addresses = [
    '北京市朝阳区建国路88号',
    '上海市浦东新区陆家嘴环路1000号',
    '广州市天河区珠江新城',
    '深圳市南山区科技园南区',
    '杭州市西湖区文三路',
  ];

  const index = Math.floor(random * stationNames.length);

  // 如果站点编号不存在，返回空数据
  if (!stationNumber || stationNumber.length < 3) {
    return {
      code: '0001',
      message: '站点不存在',
      data: null,
    };
  }

  return {
    code: '0000',
    message: 'ok',
    data: {
      stationName: stationNames[index],
      stationNumber: stationNumber,
      provinceId: index + 1,
      provinceName: ['北京', '上海', '广东', '广东', '浙江'][index],
      cityId: (index + 1) * 10 + 1,
      cityName: ['北京市', '上海市', '广州市', '深圳市', '杭州市'][index],
      countryId: (index + 1) * 100 + 11,
      countryName: ['朝阳区', '浦东新区', '天河区', '南山区', '西湖区'][index],
      address: addresses[index],
      provinceCompanyCode: `PC${String(index + 1).padStart(3, '0')}`,
      provinceCompanyName: [
        '北京省区',
        '上海省区',
        '广东省区',
        '广东省区',
        '浙江省区',
      ][index],
      areaCode: `AC${String(index + 1).padStart(3, '0')}`,
      areaName: ['北京北区', '上海东区', '广州片区', '深圳片区', '杭州片区'][
        index
      ],
      contact: contacts[index],
      contactPhone: phones[index],
      mail: `${contacts[index].toLowerCase()}@jd.com`,
    },
  };
};

// 根据车号获取车架&车型Mock数据生成函数
const generateVehicleDetailByName = (vehicleName: string) => {
  const vehicleTypes = [
    { id: 8, name: '易咖-0.5T-V2.0-大疆雷达4-灰点环视4-灰点红绿灯2-00' },
    { id: 19, name: '智梭-0.8T-V3.0-一径雷达4-森云环视4-森云红绿灯2-00' },
    {
      id: 33,
      name: '最大车型最大车型最大车型最大车型最大车型最大车型最大车型最大车型最大车型最大车型最大车型最大车型最大',
    },
    { id: 78, name: '金龙-0.8T-V2.0-大疆雷达4-灰点环视2-灰点红绿灯2-00' },
    { id: 82, name: '易咖-售卖车-V3.0-一径雷达4-森云环视4-森云红绿灯2-00' },
  ];

  // 如果车号不存在或格式不正确，返回错误
  if (!vehicleName || vehicleName.length < 3) {
    return {
      code: '0001',
      message: '车号不存在或格式不正确',
      data: null,
    };
  }

  // 根据车号生成固定的数据（模拟真实场景）
  const hash = vehicleName
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const typeIndex = hash % vehicleTypes.length;
  const serialSuffix = String(hash % 10000).padStart(4, '0');

  return {
    code: '0000',
    message: 'ok',
    data: {
      vehicleName: vehicleName,
      serialNo: `SN${vehicleName.toUpperCase()}${serialSuffix}`,
      deviceTypeId: vehicleTypes[typeIndex].id,
      deviceTypeName: vehicleTypes[typeIndex].name,
    },
  };
};

const commonApi = new CommonApi();
export { CommonApi, commonApi };
